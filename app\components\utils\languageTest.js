/***********************************************************************************
* This file is part of Visual Define-XML Editor. A program which allows to review  *
* and edit XML files created using the CDISC Define-XML standard.                  *
* Copyright (C) 2018 Dmitry Kolosov                                                *
*                                                                                  *
* Visual Define-XML Editor is free software: you can redistribute it and/or modify *
* it under the terms of version 3 of the GNU Affero General Public License         *
*                                                                                  *
* Visual Define-XML Editor is distributed in the hope that it will be useful,      *
* but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY   *
* or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License   *
* version 3 (http://www.gnu.org/licenses/agpl-3.0.txt) for more details.           *
***********************************************************************************/

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Typography, Box } from '@material-ui/core';

const LanguageTest = () => {
    const { t, i18n } = useTranslation();

    const changeLanguage = (lng) => {
        i18n.changeLanguage(lng);
    };

    return (
        <Box p={2}>
            <Typography variant="h4" gutterBottom>
                Language Test / 语言测试
            </Typography>
            
            <Typography variant="h6" gutterBottom>
                Current Language: {i18n.language}
            </Typography>
            
            <Box mb={2}>
                <Button 
                    variant="contained" 
                    color="primary" 
                    onClick={() => changeLanguage('en')}
                    style={{ marginRight: 8 }}
                >
                    English
                </Button>
                <Button 
                    variant="contained" 
                    color="secondary" 
                    onClick={() => changeLanguage('zh')}
                >
                    中文
                </Button>
            </Box>
            
            <Typography variant="body1" gutterBottom>
                Menu Items:
            </Typography>
            <ul>
                <li>{t('menu.studies')}</li>
                <li>{t('menu.editor')}</li>
                <li>{t('menu.settings')}</li>
                <li>{t('menu.about')}</li>
            </ul>
            
            <Typography variant="body1" gutterBottom>
                Settings:
            </Typography>
            <ul>
                <li>{t('settings.general')}</li>
                <li>{t('settings.language')}</li>
                <li>{t('settings.userName')}</li>
            </ul>
        </Box>
    );
};

export default LanguageTest;
