/***********************************************************************************
* This file is part of Visual Define-XML Editor. A program which allows to review  *
* and edit XML files created using the CDISC Define-XML standard.                  *
* Copyright (C) 2018 Dmitry Kolosov                                                *
*                                                                                  *
* Visual Define-XML Editor is free software: you can redistribute it and/or modify *
* it under the terms of version 3 of the GNU Affero General Public License         *
*                                                                                  *
* Visual Define-XML Editor is distributed in the hope that it will be useful,      *
* but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY   *
* or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License   *
* version 3 (http://www.gnu.org/licenses/agpl-3.0.txt) for more details.           *
***********************************************************************************/

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Import translation files
import enTranslation from '../locales/en/translation.json';
import zhTranslation from '../locales/zh/translation.json';

const resources = {
    en: {
        translation: enTranslation
    },
    zh: {
        translation: zhTranslation
    }
};

// Get language from localStorage or default to 'en'
const getStoredLanguage = () => {
    try {
        if (typeof localStorage !== 'undefined') {
            return localStorage.getItem('vde-language') || 'en';
        }
        return 'en';
    } catch (error) {
        return 'en';
    }
};

i18n
    .use(initReactI18next)
    .init({
        resources,
        fallbackLng: 'en',
        debug: false,
        lng: getStoredLanguage(),

        interpolation: {
            escapeValue: false, // React already does escaping
        }
    });

// Listen for language changes and sync with localStorage
i18n.on('languageChanged', (lng) => {
    try {
        if (typeof localStorage !== 'undefined') {
            localStorage.setItem('vde-language', lng);
        }
    } catch (error) {
        console.warn('Could not save language to localStorage:', error);
    }
});

export default i18n;
